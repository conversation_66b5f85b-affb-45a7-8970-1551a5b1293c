/**
 * Exchange Rate Monitor
 * Provides monitoring and debugging capabilities for exchange rate fetching
 */

import { getExchangeRateStats, clearExchangeRateCache } from "./db/exchange-rates";

interface ExchangeRateMetrics {
  totalRequests: number;
  deduplicatedRequests: number;
  cacheHits: number;
  cacheMisses: number;
  apiCallsSaved: number;
  lastResetTime: number;
}

class ExchangeRateMonitor {
  private static instance: ExchangeRateMonitor;
  private metrics: ExchangeRateMetrics = {
    totalRequests: 0,
    deduplicatedRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    apiCallsSaved: 0,
    lastResetTime: Date.now(),
  };

  private constructor() {}

  static getInstance(): ExchangeRateMonitor {
    if (!ExchangeRateMonitor.instance) {
      ExchangeRateMonitor.instance = new ExchangeRateMonitor();
    }
    return ExchangeRateMonitor.instance;
  }

  /**
   * Record a request for exchange rates
   */
  recordRequest(requestedPairs: string[], actualApiCalls: number): void {
    this.metrics.totalRequests += requestedPairs.length;
    this.metrics.apiCallsSaved += Math.max(0, requestedPairs.length - actualApiCalls);
    
    if (actualApiCalls < requestedPairs.length) {
      this.metrics.deduplicatedRequests += (requestedPairs.length - actualApiCalls);
    }

    // Log significant deduplication events
    if (requestedPairs.length > actualApiCalls) {
      console.log(
        `[ExchangeRateMonitor] Deduplication saved ${requestedPairs.length - actualApiCalls} API calls. Requested: ${requestedPairs.length}, Actual: ${actualApiCalls}`
      );
    }
  }

  /**
   * Record cache hit/miss statistics
   */
  recordCacheEvent(isHit: boolean): void {
    if (isHit) {
      this.metrics.cacheHits++;
    } else {
      this.metrics.cacheMisses++;
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): ExchangeRateMetrics & { 
    currentCacheStats: ReturnType<typeof getExchangeRateStats>;
    efficiencyPercentage: number;
    uptimeMinutes: number;
  } {
    const currentCacheStats = getExchangeRateStats();
    const totalCacheEvents = this.metrics.cacheHits + this.metrics.cacheMisses;
    const efficiencyPercentage = this.metrics.totalRequests > 0 
      ? (this.metrics.apiCallsSaved / this.metrics.totalRequests) * 100 
      : 0;
    const uptimeMinutes = (Date.now() - this.metrics.lastResetTime) / (1000 * 60);

    return {
      ...this.metrics,
      currentCacheStats,
      efficiencyPercentage,
      uptimeMinutes,
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      deduplicatedRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      apiCallsSaved: 0,
      lastResetTime: Date.now(),
    };
    console.log("[ExchangeRateMonitor] Metrics reset");
  }

  /**
   * Log current status for debugging
   */
  logStatus(): void {
    const metrics = this.getMetrics();
    console.log("[ExchangeRateMonitor] Current Status:", {
      totalRequests: metrics.totalRequests,
      apiCallsSaved: metrics.apiCallsSaved,
      efficiencyPercentage: `${metrics.efficiencyPercentage.toFixed(2)}%`,
      cacheSize: metrics.currentCacheStats.size,
      activeRequests: metrics.currentCacheStats.activeRequests,
      queueLength: metrics.currentCacheStats.queueLength,
      uptimeMinutes: `${metrics.uptimeMinutes.toFixed(2)} minutes`,
    });
  }

  /**
   * Clear all caches and reset metrics
   */
  fullReset(): void {
    clearExchangeRateCache();
    this.resetMetrics();
    console.log("[ExchangeRateMonitor] Full reset completed");
  }
}

// Export singleton instance and utility functions
export const exchangeRateMonitor = ExchangeRateMonitor.getInstance();

/**
 * Get exchange rate monitoring metrics
 */
export function getExchangeRateMonitoringMetrics() {
  return exchangeRateMonitor.getMetrics();
}

/**
 * Log current exchange rate monitoring status
 */
export function logExchangeRateStatus() {
  exchangeRateMonitor.logStatus();
}

/**
 * Reset exchange rate monitoring metrics
 */
export function resetExchangeRateMonitoring() {
  exchangeRateMonitor.resetMetrics();
}

/**
 * Perform full reset of exchange rate system
 */
export function fullResetExchangeRateSystem() {
  exchangeRateMonitor.fullReset();
}

/**
 * Record exchange rate request for monitoring
 */
export function recordExchangeRateRequest(requestedPairs: string[], actualApiCalls: number) {
  exchangeRateMonitor.recordRequest(requestedPairs, actualApiCalls);
}

/**
 * Record cache event for monitoring
 */
export function recordCacheEvent(isHit: boolean) {
  exchangeRateMonitor.recordCacheEvent(isHit);
}
