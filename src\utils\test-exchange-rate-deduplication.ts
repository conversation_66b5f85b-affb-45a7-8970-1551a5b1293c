/**
 * Test script for Exchange Rate Deduplication
 * This script simulates the scenario where multiple dashboard components
 * request the same exchange rates simultaneously
 */

import { 
  fetchMissingExchangeRates, 
  getExchangeRateStats,
  clearExchangeRateCache 
} from "./db/exchange-rates";

/**
 * Simulate multiple dashboard components requesting exchange rates simultaneously
 */
export async function testExchangeRateDeduplication() {
  console.log("🧪 Starting Exchange Rate Deduplication Test...");
  
  // Clear cache to start fresh
  clearExchangeRateCache();
  
  // Test data - common exchange rates that multiple components might need
  const testExchangeRates = [
    "USDEUR.FOREX",
    "EURRON.FOREX", 
    "USDRON.FOREX",
    "GBPEUR.FOREX",
    "JPYEUR.FOREX"
  ];

  console.log(`📊 Testing with exchange rates: ${testExchangeRates.join(", ")}`);
  
  // Get initial stats
  const initialStats = getExchangeRateStats();
  console.log("📈 Initial cache stats:", initialStats);

  // Simulate 5 dashboard components making simultaneous requests
  console.log("🚀 Simulating 5 simultaneous requests from dashboard components...");
  
  const startTime = Date.now();
  
  const promises = [
    // Component 1: Portfolio Metrics
    fetchMissingExchangeRates(testExchangeRates),
    // Component 2: Portfolio Composition  
    fetchMissingExchangeRates(testExchangeRates),
    // Component 3: Portfolio Performance
    fetchMissingExchangeRates(testExchangeRates),
    // Component 4: Dividends Card
    fetchMissingExchangeRates(testExchangeRates),
    // Component 5: Companies Data Table
    fetchMissingExchangeRates(testExchangeRates),
  ];

  try {
    // Wait for all requests to complete
    await Promise.all(promises);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Get final stats
    const finalStats = getExchangeRateStats();
    
    console.log("✅ All requests completed successfully!");
    console.log(`⏱️  Total duration: ${duration}ms`);
    console.log("📈 Final cache stats:", finalStats);
    
    // Calculate deduplication effectiveness
    const totalRequested = 5 * testExchangeRates.length; // 5 components × 5 exchange rates
    const actualCacheEntries = finalStats.size;
    const deduplicationSavings = totalRequested - actualCacheEntries;
    const efficiencyPercentage = (deduplicationSavings / totalRequested) * 100;
    
    console.log("📊 Deduplication Results:");
    console.log(`   • Total requests made: ${totalRequested}`);
    console.log(`   • Actual API calls: ${actualCacheEntries}`);
    console.log(`   • API calls saved: ${deduplicationSavings}`);
    console.log(`   • Efficiency: ${efficiencyPercentage.toFixed(2)}%`);
    
    if (efficiencyPercentage > 70) {
      console.log("🎉 EXCELLENT: Deduplication is working very effectively!");
    } else if (efficiencyPercentage > 50) {
      console.log("✅ GOOD: Deduplication is working well!");
    } else if (efficiencyPercentage > 0) {
      console.log("⚠️  MODERATE: Some deduplication is happening, but could be improved");
    } else {
      console.log("❌ POOR: No deduplication detected - there may be an issue");
    }
    
    return {
      success: true,
      totalRequested,
      actualApiCalls: actualCacheEntries,
      apiCallsSaved: deduplicationSavings,
      efficiencyPercentage,
      duration,
    };
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Test sequential vs parallel requests to demonstrate the difference
 */
export async function testSequentialVsParallel() {
  console.log("🧪 Testing Sequential vs Parallel Request Performance...");
  
  const testRates = ["USDEUR.FOREX", "EURRON.FOREX", "GBPEUR.FOREX"];
  
  // Test 1: Sequential requests (old behavior simulation)
  console.log("📈 Testing sequential requests...");
  clearExchangeRateCache();
  
  const sequentialStart = Date.now();
  for (const rate of testRates) {
    await fetchMissingExchangeRates([rate]);
  }
  const sequentialDuration = Date.now() - sequentialStart;
  
  // Test 2: Parallel requests with deduplication
  console.log("📈 Testing parallel requests with deduplication...");
  clearExchangeRateCache();
  
  const parallelStart = Date.now();
  await Promise.all(testRates.map(rate => fetchMissingExchangeRates([rate])));
  const parallelDuration = Date.now() - parallelStart;
  
  console.log("📊 Performance Comparison:");
  console.log(`   • Sequential: ${sequentialDuration}ms`);
  console.log(`   • Parallel: ${parallelDuration}ms`);
  console.log(`   • Improvement: ${((sequentialDuration - parallelDuration) / sequentialDuration * 100).toFixed(2)}%`);
  
  return {
    sequentialDuration,
    parallelDuration,
    improvement: (sequentialDuration - parallelDuration) / sequentialDuration * 100,
  };
}

/**
 * Run all tests
 */
export async function runAllExchangeRateTests() {
  console.log("🚀 Running comprehensive exchange rate deduplication tests...");
  
  try {
    const deduplicationTest = await testExchangeRateDeduplication();
    const performanceTest = await testSequentialVsParallel();
    
    console.log("🎉 All tests completed!");
    
    return {
      deduplication: deduplicationTest,
      performance: performanceTest,
    };
  } catch (error) {
    console.error("❌ Test suite failed:", error);
    throw error;
  }
}
